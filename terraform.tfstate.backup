{"version": 4, "terraform_version": "1.12.2", "serial": 1912, "lineage": "8f502bf4-b3b4-d92b-26d2-3e174e7dddb0", "outputs": {}, "resources": [], "check_results": [{"object_kind": "var", "config_addr": "module.enterprise_scale.var.policy_non_compliance_message_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.policy_non_compliance_message_not_enforced_replacement", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.connectivity_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_group_archetypes.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.subscription_id_management", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.root_name", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.custom_landing_zones", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.subscription_id_identity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.subscription_id_connectivity", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.destroy_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_group_archetypes.var.scope_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.connectivity_resources.var.resource_prefix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.policy_non_compliance_message_default", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.create_duration_delay", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.connectivity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.azure_ad_groups.var.groups", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.management_resources.var.subscription_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.policy_non_compliance_message_enforcement_placeholder", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.connectivity_resources.var.resource_suffix", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.module.identity_resources.var.root_id", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.enterprise_scale.var.root_parent_id", "status": "unknown", "objects": null}]}