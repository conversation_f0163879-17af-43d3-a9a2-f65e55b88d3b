{"name": "Deny-Resource-Locations", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "Specifies the allowed locations (regions) where Resources can be deployed using custom location policy.", "displayName": "Limit allowed locations for Resources (Custom)", "notScopes": [], "parameters": {"Deny-Resource-Locations": {"value": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Deny-RSG-Locations": {"value": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/location", "nonComplianceMessages": [{"message": "Resources {enforcementMode} only be deployed to the allowed locations."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}