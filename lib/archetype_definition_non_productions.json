{"non_productions": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Require-Mandatory-Tags", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs"], "policy_definitions": ["block_public_ip_assignment_to_vms", "enforce_network_security_groups", "location", "require_mandatory_tags", "secure_storage_accounts"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"allowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Deny-RSG-Locations": {"allowedLocations": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "create_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resources_type": "ResourceType", "priority": "Priority", "data_zone": "DataZone"}}, "access_control": {}}}}