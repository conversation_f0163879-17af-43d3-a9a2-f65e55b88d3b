{"name": "Block-Public-IP-Assignment-To-VMs", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy denies the network interfaces which are configured with any public IP. Public IP addresses allow internet resources to communicate inbound to Azure resources, and Azure resources to communicate outbound to the internet.", "displayName": "Network interfaces should not have public IPs", "notScopes": [], "parameters": {}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/block_public_ip_assignment_to_vms", "nonComplianceMessages": [{"message": "Network interfaces {enforcementMode} not have public IPs assigned."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}