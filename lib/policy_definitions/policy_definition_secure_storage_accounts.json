{"name": "secure_storage_accounts", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Require secure transfer on Storage Accounts", "description": "Ensure secure transfer (HTTPS only) is enabled on storage accounts", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Storage", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Policy effect", "description": "Audit or Deny if HTTPS-only is not enabled"}, "allowedValues": ["Audit", "<PERSON><PERSON>"], "defaultValue": "Audit"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "equals": false}]}, "then": {"effect": "[parameters('effect')]"}}}}