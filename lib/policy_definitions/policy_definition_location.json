{"name": "location", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Allows location for EWH resources", "description": "Resources must be in the allowed locations", "policyType": "Custom", "mode": "All", "metadata": {"category": "Tags", "version": "1.0.0"}, "parameters": {"Deny-Resource-Locations": {"type": "Array", "metadata": {"displayName": "Allowed Locations for Resources", "description": "List of allowed Azure locations for resources"}}, "Deny-RSG-Locations": {"type": "Array", "metadata": {"displayName": "Allowed Locations for Resource Groups", "description": "List of allowed Azure locations for resource groups"}}}, "policyRule": {"if": {"anyOf": [{"field": "location", "notIn": "[parameters('Deny-Resource-Locations')]"}, {"field": "location", "notIn": "[parameters('Deny-RSG-Locations')]"}]}, "then": {"effect": "<PERSON><PERSON>"}}}}