{"name": "enforce_network_security_groups", "type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "scope": null, "properties": {"displayName": "Enforce Network Security Groups on subnets", "policyType": "Custom", "mode": "All", "description": "This policy ensures that all subnets have Network Security Groups attached, except for specific excluded subnets like GatewaySubnet, AzureFirewallSubnet, etc.", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {"excludedSubnets": {"type": "Array", "metadata": {"displayName": "Excluded subnets", "description": "The list of subnet names to exclude from the policy"}, "defaultValue": ["GatewaySubnet", "AzureFirewallSubnet", "AzureBastionSubnet", "AzureFirewallManagementSubnet", "YourCustomSubnet"]}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks"}, {"not": {"field": "Microsoft.Network/virtualNetworks/subnets[*].networkSecurityGroup.id", "exists": true}}, {"field": "Microsoft.Network/virtualNetworks/subnets[*].name", "notIn": "[parameters('excludedSubnets')]"}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks/subnets"}, {"not": {"field": "Microsoft.Network/virtualNetworks/subnets/networkSecurityGroup.id", "exists": true}}, {"field": "name", "notIn": "[parameters('excludedSubnets')]"}]}]}, "then": {"effect": "deny"}}}}