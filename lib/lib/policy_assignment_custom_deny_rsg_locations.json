{"name": "Deny-RSG-Locations", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "Specifies the allowed locations (regions) where Resource Groups can be deployed using custom location policy.", "displayName": "Limit allowed locations for Resource Groups (Custom)", "notScopes": [], "parameters": {"Deny-Resource-Locations": {"value": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}, "Deny-RSG-Locations": {"value": ["eastasia", "southeastasia", "eastus", "eastus2", "west<PERSON>", "westus2"]}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/location", "nonComplianceMessages": [{"message": "Resource groups {enforcementMode} only be deployed to the allowed locations."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}