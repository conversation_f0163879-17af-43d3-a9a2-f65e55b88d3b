{"name": "Require-Mandatory-Tags", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy requires mandatory tags on resources to ensure proper governance and cost management.", "displayName": "Require mandatory tags on resources", "notScopes": [], "parameters": {"Owner": {"value": "Owner"}, "org": {"value": "Organization"}, "create_by": {"value": "CreatedBy"}, "operation_team": {"value": "OperationTeam"}, "project_name": {"value": "ProjectName"}, "env": {"value": "Environment"}, "app_name": {"value": "ApplicationName"}, "resources_type": {"value": "ResourceType"}, "priority": {"value": "Priority"}, "data_zone": {"value": "DataZone"}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/require_mandatory_tags", "nonComplianceMessages": [{"message": "Resources {enforcementMode} have all mandatory tags assigned."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}