{"name": "Enforce-Network-Security-Groups", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "This policy ensures that all subnets have Network Security Groups attached, except for specific excluded subnets like GatewaySubnet, AzureFirewallSubnet, etc.", "displayName": "Enforce Network Security Groups on subnets", "notScopes": [], "parameters": {"excludedSubnets": {"value": ["GatewaySubnet", "AzureFirewallSubnet", "AzureBastionSubnet", "AzureFirewallManagementSubnet"]}}, "policyDefinitionId": "${root_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/enforce_network_security_groups", "nonComplianceMessages": [{"message": "Subnets {enforcementMode} have Network Security Groups attached."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}